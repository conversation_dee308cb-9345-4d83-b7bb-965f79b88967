<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use App\Models\Booking;
use App\Models\ActivityLog;
use App\Models\Expense;
use App\Models\Payment;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PageController extends Controller
{
    public function admin_index()
    {
        // Get total payments by date
        $payments = Payment::selectRaw('DATE(payment_date) as date, SUM(amount) as total')
            ->groupBy('date')
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->date => $item->total];
            });

        // Get total expenses by date
        $expenses = Expense::selectRaw('DATE(expense_date) as date, SUM(amount) as total')
            ->groupBy('date')
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->date => $item->total];
            });


        // Combine dates from both payments and expenses for consistent x-axis
        $dates = $payments->keys()->merge($expenses->keys())->unique();

        // Prepare data for both series
        $paymentData = $dates->map(function ($date) use ($payments) {
            return $payments->get($date, 0);
        });

        $expenseData = $dates->map(function ($date) use ($expenses) {
            return $expenses->get($date, 0);
        });

        $currentMonth = now()->month;
        $currentYear = now()->year;

        // Monthly earnings (total payments for the current month)
        $monthlyEarnings = Payment::whereMonth('payment_date', $currentMonth)
            ->sum('amount');

        // Annual earnings (total payments for the current year)
        $annualEarnings = Payment::whereYear('payment_date', $currentYear)
            ->sum('amount');

        // Monthly expenses (total expenses for the current month)
        $monthlyExpenses = Expense::whereMonth('expense_date', $currentMonth)
            ->sum('amount');

        // Annual expenses (total expenses for the current year)
        $annualExpenses = Expense::whereYear('expense_date', $currentYear)
            ->sum('amount');



        // Total number of cash payments
        $totalCashPayments = Payment::where('payment_method', 'cash')->count();

        // Payments where is_remitted is 'no' and payment method is 'cash'
        $unremittedCount = Payment::where('is_remitted', 'no')->where('payment_method', 'cash')->count();
        // Percentage of unremitted payments
        $unremittedPercentage = ($unremittedCount / $totalCashPayments) * 100;



        // Remittances where confirmed_by is null and payment method is 'cash'
        $unconfirmedCount = Payment::whereNull('confirmed_by')->where('payment_method', 'cash')->count();
        // Percentage of unconfirmed remittances
        $unconfirmedPercentage = ($unconfirmedCount / $totalCashPayments) * 100;



        //Remittances where is_remitted is 'yes' and confirmed_by is not null and payment method is 'cash' and current location is 'deposited to bank'
        $depositedToBankCount = Payment::where('payment_method', 'cash')->where('is_remitted', 'yes')->where('current_location', 'deposited to bank')->count();
        // Percentage of remittances deposited to bank
        $depositedToBankPercentage = ($depositedToBankCount / $totalCashPayments) * 100;

        //Round off the percentages to 2 decimal places
        $unremittedPercentage = round($unremittedPercentage, 2);
        $unconfirmedPercentage = round($unconfirmedPercentage, 2);
        $depositedToBankPercentage = round($depositedToBankPercentage, 2);

        //Data for Pie Chart
        // Calculate the revenue for each payment method
        $paymentMethods = ['cash', 'paypal', 'gcash', 'paymaya', 'card', 'grab_pay'];
        $revenueSources = [];

        foreach ($paymentMethods as $method) {
            $revenueSources[$method] = Payment::where('payment_method', $method)->sum('amount');
        }


        return view('admin.admin_index', [
            //Chart Data
            'dates' => $dates,
            'paymentData' => $paymentData,
            'expenseData' => $expenseData,

            //Earnings and Expenses
            'monthlyEarnings' => $monthlyEarnings,
            'annualEarnings' => $annualEarnings,
            'monthlyExpenses' => $monthlyExpenses,
            'annualExpenses' => $annualExpenses,

            //Remittances and Payments percentages
            'unremittedPercentage' => $unremittedPercentage,
            'unconfirmedPercentage' => $unconfirmedPercentage,
            'depositedToBankPercentage' => $depositedToBankPercentage,

            //Pie Chart Data
            'revenueSources' => $revenueSources,

        ]);
    }


    public function fetchChartData(Request $request)
    {
        try {
            $filter = $request->input('filter');
            $startDate = $request->input('startDate');
            $endDate = $request->input('endDate');

            $paymentsQuery = Payment::query();
            $expensesQuery = Expense::query();
            $totalPayments = 0;
            $totalExpenses = 0;

            if ($startDate && $endDate) {
                $startDate = Carbon::parse($startDate)->startOfDay();
                $endDate = Carbon::parse($endDate)->endOfDay();

                $paymentsQuery->whereBetween('payment_date', [$startDate, $endDate]);
                $expensesQuery->whereBetween('expense_date', [$startDate, $endDate]);

                $totalPayments = $paymentsQuery->sum('amount');
                $totalExpenses = $expensesQuery->sum('amount');
            }

            if ($filter === 'week') {
                $paymentsQuery->whereBetween('payment_date', [now()->startOfWeek(), now()->endOfWeek()]);
                $expensesQuery->whereBetween('expense_date', [now()->startOfWeek(), now()->endOfWeek()]);

                $totalPayments = $paymentsQuery->sum('amount');
                $totalExpenses = $expensesQuery->sum('amount');
            } elseif ($filter === 'month') {
                $paymentsQuery->whereMonth('payment_date', now()->month);
                $expensesQuery->whereMonth('expense_date', now()->month);

                $totalPayments = $paymentsQuery->sum('amount');
                $totalExpenses = $expensesQuery->sum('amount');
            } elseif ($filter === 'year') {
                $paymentsQuery->whereYear('payment_date', now()->year);
                $expensesQuery->whereYear('expense_date', now()->year);

                $totalPayments = $paymentsQuery->sum('amount');
                $totalExpenses = $expensesQuery->sum('amount');
            }

            $payments = $paymentsQuery->get(['payment_date', 'amount']);
            $expenses = $expensesQuery->get(['expense_date', 'amount']);

            $dates = [];
            $paymentData = [];
            $expenseData = [];

            foreach ($payments as $payment) {
                $date = Carbon::parse($payment->payment_date)->format('Y-m-d');
                if (!in_array($date, $dates)) {
                    $dates[] = $date;
                }
                $paymentData[$date] = ($paymentData[$date] ?? 0) + $payment->amount;
            }

            foreach ($expenses as $expense) {
                $date = Carbon::parse($expense->expense_date)->format('Y-m-d');
                if (!in_array($date, $dates)) {
                    $dates[] = $date;
                }
                $expenseData[$date] = ($expenseData[$date] ?? 0) + $expense->amount;
            }

            sort($dates);

            $paymentData = array_map(fn($date) => $paymentData[$date] ?? 0, $dates);
            $expenseData = array_map(fn($date) => $expenseData[$date] ?? 0, $dates);

            return response()->json([
                'dates' => $dates,
                'paymentData' => $paymentData,
                'expenseData' => $expenseData,
                'totalPayments' => $totalPayments,
                'totalExpenses' => $totalExpenses
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching chart data: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred while fetching data.'], 500);
        }
    }








    public function activity_logs()
    {
        $activityLogs = ActivityLog::with('user')->latest()->get();
        return view('admin.activity_logs.view-activity_logs', compact('activityLogs'));
    }

    public function generateQrCode($booking_id, $action)
    {
        // Get booking details
        $booking = Booking::with('room', 'roomType')->findOrFail($booking_id);

        // Generate URLs based on action
        $actionUrls = [
            'invoice' => route('admin.invoice.add', $booking_id),
            'pay'     => route('admin.payment.pay', $booking_id),
        ];

        // Check if the action URL exists
        if (!isset($actionUrls[$action])) {
            abort(404, 'Action not found');
        }

        // Generate QR code URL
        $qrCodeUrl = QrCode::format('png')->size(300)->generate($actionUrls[$action]);

        return response($qrCodeUrl)->header('Content-Type', 'image/png');
    }
}
