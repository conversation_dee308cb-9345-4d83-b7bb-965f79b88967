<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Room extends Model
{
    use HasFactory;
    protected $table = 'rooms';
    protected $fillable = ['room_type', 'room_number', 'floor', 'individual_room_rate', 'is_booked', 'booked_at', 'booked_until', 'guest_id'];

    public function roomType()
    {
        return $this->belongsTo(RoomType::class, 'room_type', 'id');
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }
}
