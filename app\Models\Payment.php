<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasFactory;
    protected $table = 'payments';
    protected $fillable = ['invoice_id', 'amount', 'payment_method', 'payment_date', 'created_by', 'is_remitted', 'current_location'];

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function remittances()
    {
        return $this->hasMany(Remittance::class);
    }
}
