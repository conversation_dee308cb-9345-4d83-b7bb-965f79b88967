<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdditionalCharge extends Model
{
    use HasFactory;
    protected $table = 'additional_charges';
    protected $fillable = [
        'invoice_id',
        'order_id', //vince added
        'description',
        'amount',
        
    ];

    public function order() {//vince added function
        return $this->hasOne(Order::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }
}
