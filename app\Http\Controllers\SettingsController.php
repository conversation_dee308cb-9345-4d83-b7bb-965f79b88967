<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Setting;

class SettingsController extends Controller
{
    public function general()
    {
        return view('admin.settings.general');
    }

    public function payment()
    {
        return view('admin.settings.payment');
    }

    
    public function update(Request $request)
    {
        // Loop through all the request input data
        foreach ($request->except('_token') as $key => $value) {
            // Save or update each setting in the database
            Setting::updateOrCreate(['key' => $key], ['value' => $value]);
        }

        //Log activity
        log_activity("Settings updated", 'Settings Module');

        return back()->with('success', 'Settings updated successfully.');
    }

    // Add more methods as needed
}
