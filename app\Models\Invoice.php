<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    use HasFactory;

    protected $table = 'invoices';
    protected $fillable = ['booking_id', 'total_amount', 'invoice_date', 'status', 'created_by', 'updated_by'];

    protected $casts = [
        'status' => 'string',
    ];
    

    public function booking()
    {
        return $this->belongsTo(Booking::class, 'booking_id', 'id');
    }
    public function order()//vince added
    {
        return $this->hasOne(Order::class, 'invoice_id', 'id');
    }

    public function additionalCharges()
    {
        return $this->hasMany(AdditionalCharge::class);
    }

    // Invoice.php
    public function payments()
    {
        return $this->hasMany(Payment::class, 'invoice_id', 'id');
    }

    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }
    
}
