<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Event extends Model
{
    use HasFactory;
    protected $fillable = [
        'customer_first_name',
        'customer_last_name',
        'customer_email',
        'customer_phone',
        'room_id',
        'booking_id',
        'name',
        'description',
        'start_date',
        'end_date',
        'created_by',
        'updated_by',
        'status',
        'sub_total',
    ];

    public function room()
    {
        return $this->belongsTo(Room::class);
    }

    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    public function eventItems()
    {
        return $this->hasMany(Event_Item::class);
    }

    public function groceries()
    {
        return $this->hasMany(Grocery_Item::class);
    }
}
