<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Guest extends Model
{
    use HasFactory;
    protected $table = 'guests';
    protected $fillable = ['first_name', 'last_name', 'email', 'phone', 'address', 'notes'];

    public function bookings()//vince added function
    {
        return $this->hasMany(Booking::class);
    }
}
