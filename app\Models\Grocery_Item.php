<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Grocery_Item extends Model
{
    use HasFactory;
    protected $table = 'grocery_items';
    protected $fillable = [
        'menu_id',
        'event_id',
        'event_item_id',
        'ingredient_id',
        'quantity',
        'unit',
        'cost',
        'total_cost',
    ];

    public function menu()
    {
        return $this->belongsTo(Menu::class, 'menu_id');
    }

    public function event()
    {
        return $this->belongsTo(Event::class, 'event_id');
    }

    public function eventItem()
    {
        return $this->belongsTo(Event_Item::class, 'event_item_id');
    }

    public function ingredient()
    {
        return $this->belongsTo(Ingredient::class, 'ingredient_id');
    }

    public function expenses()
    {
        return $this->hasMany(Expense::class, 'grocery_item_id');
    }
}
