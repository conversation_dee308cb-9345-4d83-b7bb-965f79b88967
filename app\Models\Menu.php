<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Menu extends Model
{
    protected $table = 'menus'; // Specify the custom table name
    protected $fillable = [
        'id',
        'name',
        'description',
        'how_to_cook',
        'price',
        'cook_time',
        'created_at',
        'updated_at',
    ];

    public function ingredients()
    {
        return $this->belongsToMany(Ingredient::class, 'menu_ingredients', 'menu_id', 'ingredient_id')
            ->withPivot('quantity', 'unit');
    }
}
